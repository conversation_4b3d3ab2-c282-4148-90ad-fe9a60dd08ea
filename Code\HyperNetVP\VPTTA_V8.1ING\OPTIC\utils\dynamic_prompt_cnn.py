import torch
import torch.nn as nn
import torch.nn.functional as F


class DynamicPromptCNN(nn.Module):
    """
    动态提示CNN - 权重由超网络动态生成的3层卷积网络

    架构：6通道频域输入 → 3层卷积 → 3×prompt_size×prompt_size提示矩阵
    注意：此类不包含任何可训练参数，所有权重由外部提供
    """
    
    def __init__(self, prompt_size=5, prompt_min_val=0.5, prompt_max_val=1.5):
        """
        初始化动态CNN
        
        Args:
            prompt_size (int): 输出提示矩阵尺寸
            prompt_min_val (float): 提示值最小值
            prompt_max_val (float): 提示值最大值
        """
        super().__init__()
        self.prompt_size = prompt_size
        self.prompt_min_val = prompt_min_val
        self.prompt_max_val = prompt_max_val
        
        # 定义网络结构参数
        self.layer_configs = [
            # Layer1: Conv2d(6, 32, 7, stride=8, padding=3)
            {
                'in_channels': 6,
                'out_channels': 32,
                'kernel_size': 7,
                'stride': 8,
                'padding': 3,
                'weight_shape': (32, 6, 7, 7),
                'bias_shape': (32,)
            },
            # Layer2: Conv2d(32, 16, 5, stride=8, padding=2)
            {
                'in_channels': 32,
                'out_channels': 16,
                'kernel_size': 5,
                'stride': 8,
                'padding': 2,
                'weight_shape': (16, 32, 5, 5),
                'bias_shape': (16,)
            },
            # Layer3: Conv2d(16, 3, 4, stride=1, padding=0)
            {
                'in_channels': 16,
                'out_channels': 3,
                'kernel_size': 4,
                'stride': 1,
                'padding': 0,
                'weight_shape': (3, 16, 4, 4),
                'bias_shape': (3,)
            }
        ]
        
        # 计算参数分割索引
        self.param_splits = self._calculate_param_splits()
    
    def _calculate_param_splits(self):
        """计算参数分割索引，用于从超网络输出中提取各层参数"""
        splits = []
        current_idx = 0
        
        for config in self.layer_configs:
            weight_size = torch.Size(config['weight_shape']).numel()
            bias_size = torch.Size(config['bias_shape']).numel()
            
            weight_end = current_idx + weight_size
            bias_end = weight_end + bias_size
            
            splits.append({
                'weight_start': current_idx,
                'weight_end': weight_end,
                'bias_start': weight_end,
                'bias_end': bias_end,
                'weight_shape': config['weight_shape'],
                'bias_shape': config['bias_shape']
            })
            
            current_idx = bias_end
        
        return splits
    
    def _extract_layer_params(self, all_params, layer_idx):
        """
        从超网络输出中提取指定层的权重和偏置
        
        Args:
            all_params (torch.Tensor): 超网络输出的所有参数 (B, total_params)
            layer_idx (int): 层索引
            
        Returns:
            tuple: (weight, bias)
        """
        split_info = self.param_splits[layer_idx]
        batch_size = all_params.size(0)
        
        # 提取权重
        weight_flat = all_params[:, split_info['weight_start']:split_info['weight_end']]
        weight = weight_flat.view(batch_size, *split_info['weight_shape'])
        
        # 提取偏置
        bias_flat = all_params[:, split_info['bias_start']:split_info['bias_end']]
        bias = bias_flat.view(batch_size, *split_info['bias_shape'])
        
        return weight, bias
    
    def _dynamic_conv2d(self, x, weight, bias, stride, padding):
        """
        使用动态权重执行卷积操作
        
        Args:
            x (torch.Tensor): 输入特征 (B, C_in, H, W)
            weight (torch.Tensor): 卷积权重 (B, C_out, C_in, K, K)
            bias (torch.Tensor): 偏置 (B, C_out)
            stride (int): 步长
            padding (int): 填充
            
        Returns:
            torch.Tensor: 卷积输出 (B, C_out, H', W')
        """
        batch_size = x.size(0)
        outputs = []
        
        # 对batch中每个样本分别执行卷积
        for i in range(batch_size):
            # 单个样本的输入和权重
            x_i = x[i:i+1]  # (1, C_in, H, W)
            weight_i = weight[i]  # (C_out, C_in, K, K)
            bias_i = bias[i]  # (C_out,)
            
            # 执行卷积
            out_i = F.conv2d(x_i, weight_i, bias_i, stride=stride, padding=padding)
            outputs.append(out_i)
        
        # 拼接所有样本的输出
        return torch.cat(outputs, dim=0)
    
    def forward(self, x, dynamic_params):
        """
        前向传播：使用动态参数执行卷积

        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            dynamic_params (torch.Tensor): 超网络生成的参数 (B, total_params)

        Returns:
            torch.Tensor: 提示矩阵 (B, 3, prompt_size, prompt_size)
        """
        # FFT变换提取频谱
        fft = torch.fft.fft2(x, dim=(-2, -1))
        amp_spectrum = torch.abs(fft)
        phase_spectrum = torch.angle(fft)
        
        # fftshift将低频移到中心
        amp_spectrum = torch.fft.fftshift(amp_spectrum)
        phase_spectrum = torch.fft.fftshift(phase_spectrum)
        
        # 拼接为6通道输入
        freq_input = torch.cat([amp_spectrum, phase_spectrum], dim=1)
        
        # 逐层执行动态卷积
        current_input = freq_input
        
        for i, config in enumerate(self.layer_configs):
            # 提取当前层参数
            weight, bias = self._extract_layer_params(dynamic_params, i)
            
            # 执行动态卷积
            current_input = self._dynamic_conv2d(
                current_input, weight, bias,
                stride=config['stride'],
                padding=config['padding']
            )
            
            # 添加激活函数（除了最后一层）
            if i < len(self.layer_configs) - 1:
                # InstanceNorm2d + LeakyReLU
                current_input = F.instance_norm(current_input)
                current_input = F.leaky_relu(current_input, 0.2, inplace=True)
        
        # 最终输出应用sigmoid约束
        prompt = torch.sigmoid(current_input) * (self.prompt_max_val - self.prompt_min_val) + self.prompt_min_val
        
        # 验证输出尺寸
        expected_shape = (x.size(0), 3, self.prompt_size, self.prompt_size)
        if prompt.shape != expected_shape:
            raise RuntimeError(f"DynamicPromptCNN output size mismatch: {prompt.shape} vs expected {expected_shape}")
        
        return prompt
    
    def get_total_param_count(self):
        """
        获取总参数数量
        
        Returns:
            int: 总参数数量
        """
        total = 0
        for split_info in self.param_splits:
            weight_size = torch.Size(split_info['weight_shape']).numel()
            bias_size = torch.Size(split_info['bias_shape']).numel()
            total += weight_size + bias_size
        
        return total
