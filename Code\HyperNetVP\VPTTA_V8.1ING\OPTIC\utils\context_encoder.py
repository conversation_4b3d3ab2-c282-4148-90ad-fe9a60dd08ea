import torch
import torch.nn as nn
import torch.nn.functional as F
import os
from networks.resnet import resnet34


class ContextEncoder(nn.Module):
    """
    情境编码器 - 使用冻结的ResNet34前两层提取情境特征

    架构：输入图像 → ResNet34前两层（冻结） → 全局平均池化 → 情境向量
    """

    def __init__(self, context_dim=64, model_root=None):
        """
        初始化情境编码器

        Args:
            context_dim (int): 输出情境向量维度
            model_root (str): 模型根目录路径，如果提供则从该路径加载预训练权重
        """
        super().__init__()
        self.context_dim = context_dim

        # 加载ResNet34，不使用默认预训练权重
        resnet = resnet34(pretrained=False)

        # 如果提供了model_root，从指定路径加载预训练权重
        if model_root is not None:
            # 尝试多个可能的ResNet34权重文件路径
            possible_paths = [
                os.path.join(model_root, 'resnet34-333f7ec4.pth'),
                os.path.join(model_root, 'resnet34.pth'),
                os.path.join(model_root, 'backbone', 'resnet34-333f7ec4.pth'),
                os.path.join(model_root, 'backbone', 'resnet34.pth')
            ]

            loaded = False
            for resnet34_path in possible_paths:
                if os.path.exists(resnet34_path):
                    print(f"Loading ResNet34 weights from: {resnet34_path}")
                    try:
                        resnet.load_state_dict(torch.load(resnet34_path), strict=False)
                        loaded = True
                        break
                    except Exception as e:
                        print(f"Failed to load weights from {resnet34_path}: {e}")
                        continue

            if not loaded:
                print(f"Warning: ResNet34 weights not found in any of the following paths:")
                for path in possible_paths:
                    print(f"  - {path}")
                print("Using random initialization for ResNet34 backbone.")
        else:
            print("No model_root provided, using random initialization for ResNet34 backbone.")
        
        # 提取前两层：conv1 + bn1 + relu + maxpool + layer1
        self.conv1 = resnet.conv1      # 3→64, 7x7, stride=2
        self.bn1 = resnet.bn1          # BatchNorm2d(64)
        self.relu = resnet.relu        # ReLU
        self.maxpool = resnet.maxpool  # MaxPool2d(3x3, stride=2)
        self.layer1 = resnet.layer1    # 64→64, 3个BasicBlock
        
        # 全局平均池化
        self.global_avgpool = nn.AdaptiveAvgPool2d((1, 1))
        
        # 冻结所有参数
        self._freeze_parameters()
        
        # 验证输出维度
        if context_dim != 64:
            raise ValueError(f"ResNet34 layer1 output is 64 channels, but context_dim={context_dim}")
    
    def _freeze_parameters(self):
        """冻结所有参数，不参与训练"""
        for param in self.parameters():
            param.requires_grad = False
    
    def forward(self, x):
        """
        前向传播：提取情境特征
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            
        Returns:
            torch.Tensor: 情境向量 (B, context_dim)
        """
        # ResNet34前两层特征提取
        x = self.conv1(x)      # (B, 64, H/2, W/2)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)    # (B, 64, H/4, W/4)
        x = self.layer1(x)     # (B, 64, H/4, W/4)
        
        # 全局平均池化得到情境向量
        context = self.global_avgpool(x)  # (B, 64, 1, 1)
        context = context.view(context.size(0), -1)  # (B, 64)
        
        return context
    
    def get_parameter_count(self):
        """
        获取参数统计信息
        
        Returns:
            dict: 参数统计
        """
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'frozen_params': total_params - trainable_params
        }
