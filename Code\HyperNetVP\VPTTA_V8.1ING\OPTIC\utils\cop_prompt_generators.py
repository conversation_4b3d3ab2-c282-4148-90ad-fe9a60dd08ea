import torch
import torch.nn as nn


class CausalPromptGenerator(nn.Module):
    """
    因果提示生成器 - 生成用于因果特征调制的参数
    
    架构：全局特征向量 → 2层MLP → γ和β调制参数
    """
    
    def __init__(self, input_dim, hidden_dim):
        """
        初始化因果提示生成器
        
        Args:
            input_dim (int): 输入全局特征维度（ResNet34: 512, ResNet50: 2048）
            hidden_dim (int): 隐藏层维度
        """
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        self.mlp = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Linear(hidden_dim, input_dim * 2)  # γ和β拼接输出
        )
        
        # 权重初始化
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, z_global):
        """
        前向传播：生成因果调制参数
        
        Args:
            z_global (torch.Tensor): 全局特征向量 (B, input_dim)
            
        Returns:
            tuple: (gamma, beta)
                gamma (torch.Tensor): 缩放参数 (B, input_dim)
                beta (torch.Tensor): 位移参数 (B, input_dim)
        """
        params = self.mlp(z_global)  # (B, input_dim * 2)
        gamma, beta = params.chunk(2, dim=1)  # 各自 (B, input_dim)
        
        # 确保gamma为正值（使用softplus）
        gamma = torch.nn.functional.softplus(gamma) + 1e-6
        
        return gamma, beta


class SpuriousPromptGenerator(nn.Module):
    """
    虚假提示生成器 - 生成用于虚假特征调制的参数
    
    架构：全局特征向量 → 2层MLP → γ和β调制参数
    注意：与CausalPromptGenerator结构相同但参数独立
    """
    
    def __init__(self, input_dim, hidden_dim):
        """
        初始化虚假提示生成器
        
        Args:
            input_dim (int): 输入全局特征维度（ResNet34: 512, ResNet50: 2048）
            hidden_dim (int): 隐藏层维度
        """
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        self.mlp = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Linear(hidden_dim, input_dim * 2)  # γ和β拼接输出
        )
        
        # 权重初始化
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, z_global):
        """
        前向传播：生成虚假调制参数
        
        Args:
            z_global (torch.Tensor): 全局特征向量 (B, input_dim)
            
        Returns:
            tuple: (gamma, beta)
                gamma (torch.Tensor): 缩放参数 (B, input_dim)
                beta (torch.Tensor): 位移参数 (B, input_dim)
        """
        params = self.mlp(z_global)  # (B, input_dim * 2)
        gamma, beta = params.chunk(2, dim=1)  # 各自 (B, input_dim)
        
        # 确保gamma为正值（使用softplus）
        gamma = torch.nn.functional.softplus(gamma) + 1e-6
        
        return gamma, beta
