import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from .hyper_prompt_generator import HyperPromptGenerator






class PromptGenerator(nn.Module):
    """
    超网络提示生成器 - 使用超网络动态生成提示CNN权重

    新架构：
    1. ContextEncoder: 冻结的ResNet34前两层提取情境特征
    2. Hypernetwork: 根据情境特征生成动态CNN权重
    3. DynamicPromptCNN: 使用动态权重生成提示矩阵
    """

    def __init__(self, prompt_alpha=0.01, image_size=512,
                 context_dim=64, hypernetwork_hidden=256,
                 prompt_min_val=0.5, prompt_max_val=1.5, model_root=None):
        """
        初始化超网络提示生成器

        Args:
            prompt_alpha (float): 提示大小比例
            image_size (int): 参考图像尺寸（用于计算prompt_size）
            context_dim (int): 情境向量维度
            hypernetwork_hidden (int): 超网络隐藏层维度
            prompt_min_val (float): 提示值最小值
            prompt_max_val (float): 提示值最大值
            model_root (str): 模型根目录路径
        """
        super().__init__()

        # 计算提示相关参数
        self.prompt_size = int(image_size * prompt_alpha) if int(image_size * prompt_alpha) > 1 else 1
        # 计算中性填充值
        self.neutral_val = 1.0

        # 初始化超网络提示生成器
        self.hyper_generator = HyperPromptGenerator(
            prompt_alpha=prompt_alpha,
            image_size=image_size,
            context_dim=context_dim,
            hypernetwork_hidden=hypernetwork_hidden,
            prompt_min_val=prompt_min_val,
            prompt_max_val=prompt_max_val,
            model_root=model_root
        )

    def generate_prompt(self, x):
        """
        生成提示矩阵（超网络版本）

        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)

        Returns:
            tuple: (prompt, None) - 第二个返回值为None保持接口兼容
        """
        return self.hyper_generator.generate_prompt(x)

    def iFFT(self, amp_src_, pha_src, imgH, imgW):
        """
        逆FFT变换方法（委托给超网络生成器）

        Args:
            amp_src_: 幅度谱
            pha_src: 相位谱
            imgH: 图像高度
            imgW: 图像宽度

        Returns:
            torch.Tensor: 重构的图像
        """
        return self.hyper_generator.iFFT(amp_src_, pha_src, imgH, imgW)



    def _apply_prompt_fft(self, x, prompt):
        """
        应用提示到图像的FFT域（委托给超网络生成器）

        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            prompt (torch.Tensor): 提示矩阵 (B, 3, prompt_size, prompt_size)

        Returns:
            tuple: (prompt_x, low_freq)
                prompt_x (torch.Tensor): 调整后的图像
                low_freq (torch.Tensor): 低频特征
        """
        return self.hyper_generator._apply_prompt_fft(x, prompt)

    def parameters(self):
        """
        返回可训练参数（仅超网络参数）

        Returns:
            generator: 参数生成器
        """
        return self.hyper_generator.parameters()

    def train(self, mode=True):
        """
        设置训练模式

        Args:
            mode (bool): 训练模式标志
        """
        self.hyper_generator.train(mode)
        return self

    def eval(self):
        """
        设置评估模式
        """
        self.hyper_generator.eval()
        return self
