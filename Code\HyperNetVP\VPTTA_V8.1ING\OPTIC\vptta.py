import os
import torch
import numpy as np
import argparse, sys, datetime
from config import Logger, seed_torch, worker_init_fn
from torch.autograd import Variable
from utils.convert import AdaBN
from utils.metrics import calculate_metrics
from utils.loss_debugger import LossDebugger
from utils.cop_losses import orthogonal_loss, domain_loss, segmentation_loss, generate_pseudo_labels
from networks.ResUnet_TTA import ResUnet
from torch.utils.data import DataLoader
from dataloaders.OPTIC_dataloader import OPTIC_dataset
from dataloaders.transform import collate_fn_wo_transform
from dataloaders.convert_csv_to_list import convert_labeled_list
import torch.nn.functional as F


torch.set_num_threads(1)


class VPTTA:
    def __init__(self, config):
        # 立即设置随机种子，确保模型初始化的可重现性
        seed_torch(config.seed)

        # Save Log
        time_now = datetime.datetime.now().__format__("%Y%m%d_%H%M%S_%f")
        log_root = os.path.join(config.path_save_log, 'VPTTA')
        if not os.path.exists(log_root):
            os.makedirs(log_root)
        log_path = os.path.join(log_root, time_now + '.log')
        sys.stdout = Logger(log_path, sys.stdout)

        # Data Loading
        target_test_csv = []
        for target in config.Target_Dataset:
            if target != 'REFUGE_Valid':
                target_test_csv.append(target + '_train.csv')
                target_test_csv.append(target + '_test.csv')
            else:
                target_test_csv.append(target + '.csv')
        ts_img_list, ts_label_list = convert_labeled_list(config.dataset_root, target_test_csv)
        target_test_dataset = OPTIC_dataset(config.dataset_root, ts_img_list, ts_label_list,
                                            config.image_size, img_normalize=True)

        # 创建worker初始化函数，捕获当前种子值
        worker_init = lambda worker_id: worker_init_fn(worker_id, config.seed)

        self.target_test_loader = DataLoader(dataset=target_test_dataset,
                                             batch_size=config.batch_size,
                                             shuffle=False,
                                             pin_memory=True,
                                             drop_last=False,
                                             collate_fn=collate_fn_wo_transform,
                                             num_workers=config.num_workers,
                                             worker_init_fn=worker_init)
        self.image_size = config.image_size

        # Model
        self.load_model = os.path.join(config.model_root, str(config.Source_Dataset))  # Pre-trained Source Model
        self.backbone = config.backbone
        self.in_ch = config.in_ch
        self.out_ch = config.out_ch

        # GPU
        self.device = config.device

        # Warm-up
        self.warm_n = config.warm_n

        # COP Framework
        self.iters = config.iters
        self.causal_lr = config.causal_lr
        self.spurious_lr = config.spurious_lr
        self.ortho_weight = config.ortho_weight
        self.domain_weight = config.domain_weight
        self.prompt_hidden_dim = config.prompt_hidden_dim

        # 初始化调试器
        self.debug_loss = getattr(config, 'debug_loss', False)
        debug_log_file = None
        if self.debug_loss:
            # 创建调试日志文件路径
            time_now = datetime.datetime.now().__format__("%Y%m%d_%H%M%S_%f")
            debug_log_file = os.path.join(config.path_save_log, 'VPTTA', f'debug_loss_{time_now}.log')
        self.loss_debugger = LossDebugger(enabled=self.debug_loss, log_file=debug_log_file)

        # Initialize the pre-trained model and optimizer
        self.build_model()

        # Print Information
        if self.debug_loss:
            print("=== Configuration Details ===")
            for arg, value in vars(config).items():
                print(f"{arg}: {value}")
        else:
            # 简洁的基础信息
            print(f"VPTTA Config: {config.Source_Dataset} -> {config.Target_Dataset}")
            print(f"Model: {config.backbone} | Device: {config.device} | Seed: {config.seed}")
        print('***' * 20)

    def build_model(self):
        # 加载主模型
        self.model = ResUnet(
            resnet=self.backbone,
            num_classes=self.out_ch,
            pretrained=False,
            newBN=AdaBN,
            warm_n=self.warm_n,
            prompt_hidden_dim=self.prompt_hidden_dim
        ).to(self.device)
        checkpoint = torch.load(os.path.join(self.load_model, 'last-Res_Unet.pth'))
        self.model.load_state_dict(checkpoint, strict=True)

        # 创建参数组：因果和虚假提示生成器分别优化
        param_groups = [
            {'params': self.model.causal_prompt_gen.parameters(), 'lr': self.causal_lr},
            {'params': self.model.spurious_prompt_gen.parameters(), 'lr': self.spurious_lr}
        ]

        self.optimizer = torch.optim.Adam(param_groups)

        # 验证COP框架配置
        if self.debug_loss:
            print("=== Debug Information ===")
            print(f"COP Framework: Causal Orthogonal Prompting")
            print(f"Architecture: Dual Prompt Generators with Feature Modulation")
            print(f"Prompt hidden dim: {self.prompt_hidden_dim}")
            print(f"Causal LR: {self.causal_lr}")
            print(f"Spurious LR: {self.spurious_lr}")
            print(f"Ortho weight: {self.ortho_weight}")
            print(f"Domain weight: {self.domain_weight}")

            causal_params = sum(p.numel() for p in self.model.causal_prompt_gen.parameters() if p.requires_grad)
            spurious_params = sum(p.numel() for p in self.model.spurious_prompt_gen.parameters() if p.requires_grad)
            print(f"Causal prompt generator params: {causal_params}")
            print(f"Spurious prompt generator params: {spurious_params}")
            print(f"Total trainable params: {causal_params + spurious_params}")
            print(f"Optimizer groups: {len(self.optimizer.param_groups)}")



    def run(self):
        metric_dict = ['Disc_Dice', 'Disc_ASD', 'Cup_Dice', 'Cup_ASD']

        # Valid on Target
        metrics_test = [[], [], [], []]

        for batch, data in enumerate(self.target_test_loader):
            x, y = data['data'], data['mask']
            x = torch.from_numpy(x).to(dtype=torch.float32)
            y = torch.from_numpy(y).to(dtype=torch.float32)

            x, y = Variable(x).to(self.device), Variable(y).to(self.device)

            self.model.eval()
            # 设置提示生成器为训练模式
            self.model.causal_prompt_gen.train()
            self.model.spurious_prompt_gen.train()
            self.model.change_BN_status(new_sample=True)

            # Train COP Framework for n iters
            for tr_iter in range(self.iters):
                # --- 1. COP双路径前向传播 ---
                seg_output, x_causal, x_spurious, z_global = self.model(x)

                # --- 2. 生成伪标签 ---
                pseudo_labels = generate_pseudo_labels(seg_output, confidence_threshold=0.9)

                # --- 3. 计算COP损失 ---
                # 分割损失（基于因果路径）
                seg_loss = segmentation_loss(seg_output, pseudo_labels)

                # 正交损失（因果与虚假特征解耦）
                ortho_loss = orthogonal_loss(x_causal, x_spurious)

                # 领域损失（虚假特征重构）
                domain_loss_val = domain_loss(x_spurious, z_global)

                # 计算bn_loss (保持现有逻辑)
                times, bn_loss = 0, 0
                for nm, m in self.model.named_modules():
                    if isinstance(m, AdaBN):
                        bn_loss += m.bn_loss
                        times += 1
                bn_loss = bn_loss / times if times > 0 else 0.0

                # 组合总损失
                total_loss = seg_loss + self.ortho_weight * ortho_loss + self.domain_weight * domain_loss_val + bn_loss

                # --- 4. 暂存损失信息用于调试 ---
                if self.debug_loss:
                    self.current_losses = {
                        'seg_loss': float(seg_loss),
                        'ortho_loss': float(ortho_loss),
                        'domain_loss': float(domain_loss_val),
                        'bn_loss': float(bn_loss),
                        'total_loss': float(total_loss)
                    }

                # --- 5. 反向传播与优化 ---
                self.optimizer.zero_grad()
                total_loss.backward()

                # 梯度调试逻辑
                if self.debug_loss:
                    total_grad_norm = 0.0
                    param_count = 0
                    has_grad = False

                    # 检查因果和虚假提示生成器的梯度
                    for name, module in [('causal', self.model.causal_prompt_gen), ('spurious', self.model.spurious_prompt_gen)]:
                        for param in module.parameters():
                            if param.grad is not None:
                                param_grad_norm = param.grad.data.norm(2)
                                total_grad_norm += param_grad_norm.item() ** 2
                                param_count += 1
                                has_grad = True

                    if has_grad and param_count > 0:
                        total_grad_norm = (total_grad_norm ** 0.5)
                        if total_grad_norm < 1e-6:
                            grad_status = 'VANISH'
                        elif total_grad_norm > 10.0:
                            grad_status = 'EXPLODE'
                        else:
                            grad_status = 'NORMAL'
                    else:
                        total_grad_norm = 0.0
                        grad_status = 'NO_GRAD'

                    self.current_grad_data = {
                        'norm': total_grad_norm,
                        'status': grad_status
                    }

                self.optimizer.step()
                self.model.change_BN_status(new_sample=False)

            # Inference
            self.model.eval()
            self.model.causal_prompt_gen.eval()
            self.model.spurious_prompt_gen.eval()
            with torch.no_grad():
                # COP推理：只使用因果路径的分割结果
                pred_logit, _, _, _ = self.model(x)

            # Calculate the metrics
            seg_output = torch.sigmoid(pred_logit)

            metrics = calculate_metrics(seg_output.detach().cpu(), y.detach().cpu())
            for i in range(len(metrics)):
                assert isinstance(metrics[i], list), "The metrics value is not list type."
                metrics_test[i] += metrics[i]

            # 记录完整的调试信息（损失+DICE分数）
            if self.debug_loss:
                # 计算当前样本的平均DICE分数
                disc_dice = np.mean(metrics[0]) if metrics[0] else 0.0
                cup_dice = np.mean(metrics[2]) if metrics[2] else 0.0

                metrics_dict = {
                    'disc_dice': float(disc_dice),
                    'cup_dice': float(cup_dice)
                }

                # 使用训练阶段保存的损失信息
                losses_dict = getattr(self, 'current_losses', {
                    'bn_loss': 0.0,
                    'total_loss': 0.0
                })

                self.loss_debugger.log_sample_debug(
                    sample_idx=batch + 1,
                    iter_idx=1,  # 对应训练的迭代
                    losses_dict=losses_dict,
                    metrics_dict=metrics_dict
                )

                # 输出健康监控信息
                # 准备健康监控数据
                cop_data = {'framework': 'COP', 'dual_path': True}
                grad_data = getattr(self, 'current_grad_data', {'norm': 0.0, 'status': 'UNKNOWN'})
                loss_stats = getattr(self, 'current_losses', {
                    'seg_loss': 0.0, 'ortho_loss': 0.0, 'domain_loss': 0.0, 'bn_loss': 0.0, 'total_loss': 0.0
                })

                self.loss_debugger.log_health_debug(
                    sample_idx=batch + 1,
                    mlp_data=cop_data,
                    grad_data=grad_data,
                    prompt_stats=loss_stats
                )

        test_metrics_y = np.mean(metrics_test, axis=1)
        print_test_metric_mean = {}
        for i in range(len(test_metrics_y)):
            print_test_metric_mean[metric_dict[i]] = test_metrics_y[i]
        print("Test Metrics: ", print_test_metric_mean)
        print('Mean Dice:', (print_test_metric_mean['Disc_Dice'] + print_test_metric_mean['Cup_Dice']) / 2)

        # 关闭调试器
        if self.debug_loss:
            self.loss_debugger.close()


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    # Dataset
    parser.add_argument('--Source_Dataset', type=str, default='RIM_ONE_r3',
                        help='RIM_ONE_r3/REFUGE/ORIGA/REFUGE_Valid/Drishti_GS')
    parser.add_argument('--Target_Dataset', type=list)

    parser.add_argument('--num_workers', type=int, default=8)
    parser.add_argument('--image_size', type=int, default=512)

    # Model
    parser.add_argument('--backbone', type=str, default='resnet34', help='resnet34/resnet50')
    parser.add_argument('--in_ch', type=int, default=3)
    parser.add_argument('--out_ch', type=int, default=2)

    # Training
    parser.add_argument('--batch_size', type=int, default=1)
    parser.add_argument('--iters', type=int, default=1)

    # Warm-up statistics
    parser.add_argument('--warm_n', type=int, default=5)

    # COP Framework parameters
    parser.add_argument('--causal_lr', type=float, default=0.001,
                        help='Learning rate for causal prompt generator')
    parser.add_argument('--spurious_lr', type=float, default=0.001,
                        help='Learning rate for spurious prompt generator')
    parser.add_argument('--ortho_weight', type=float, default=0.1,
                        help='Weight for orthogonal loss')
    parser.add_argument('--domain_weight', type=float, default=0.05,
                        help='Weight for domain loss')
    parser.add_argument('--prompt_hidden_dim', type=int, default=256,
                        help='Hidden dimension for prompt generators')

    # Path
    parser.add_argument('--path_save_log', type=str, default='./logs')
    parser.add_argument('--model_root', type=str, default='./models')
    parser.add_argument('--dataset_root', type=str, default='/media/userdisk0/zychen/Datasets/Fundus')

    # Cuda (default: the first available device)
    parser.add_argument('--device', type=str, default='cuda:0')



    # 调试相关参数
    parser.add_argument('--debug_loss', action='store_true',
                        help='Enable loss and performance debugging output.')

    # 可重现性相关参数
    parser.add_argument('--seed', type=int, default=42,
                        help='Random seed for reproducible model initialization.')

    config = parser.parse_args()

    config.Target_Dataset = ['RIM_ONE_r3', 'REFUGE', 'ORIGA', 'REFUGE_Valid', 'Drishti_GS']
    config.Target_Dataset.remove(config.Source_Dataset)

    TTA = VPTTA(config)
    TTA.run()
