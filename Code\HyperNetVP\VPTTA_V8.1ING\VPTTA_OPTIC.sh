
#!/bin/bash

#Please modify the following roots to yours.
dataset_root=/opt/data/private/zjw/Data/Fundus
model_root=/opt/data/private/zjw/Data/models
path_save_log=/opt/data/private/zjw/VPTTA-main/OPTIC/logs/


#Dataset [RIM_ONE_r3, REFUGE, ORIGA, REFUGE_Valid, Drishti_GS]
Source=ORIGA

#Hyperparameters
warm_n=5

#COP Framework Parameters
causal_lr=0.001
spurious_lr=0.001
ortho_weight=0.1
domain_weight=0.05
prompt_hidden_dim=256

#Debug Parameters
enable_debug=true

#Reproducibility Parameters
seed=42

#Command
cd OPTIC

# 构建调试参数
debug_args=""
if [ "$enable_debug" = "true" ]; then
    debug_args="--debug_loss"
fi



CUDA_VISIBLE_DEVICES=0 python vptta.py \
--dataset_root $dataset_root --model_root $model_root --path_save_log $path_save_log \
--Source_Dataset $Source \
--warm_n $warm_n \
--causal_lr $causal_lr \
--spurious_lr $spurious_lr \
--ortho_weight $ortho_weight \
--domain_weight $domain_weight \
--prompt_hidden_dim $prompt_hidden_dim \
--seed $seed \
$debug_args