
#!/bin/bash

#Please modify the following roots to yours.
dataset_root=/opt/data/private/zjw/Data/Fundus
model_root=/opt/data/private/zjw/Data/models
path_save_log=/opt/data/private/zjw/VPTTA-main/OPTIC/logs/


#Dataset [RIM_ONE_r3, REFUGE, ORIGA, REF<PERSON>GE_Valid, Dr<PERSON>ti_GS]
Source=ORIGA

#Hyperparameters
prompt_alpha=0.01
warm_n=5

#Prompt Generator
generator_lr=0.0005

# Entropy minimization parameters
entropy_weight=0.5
entropy_temperature=1.0

#Hypernetwork Parameters
context_dim=64
hypernetwork_hidden=256
#强制中性值为1了 在代码里面
prompt_min_val=0.5
prompt_max_val=1.5

#Debug Parameters
enable_debug=true

#Reproducibility Parameters
seed=42

#Command
cd OPTIC

# 构建调试参数
debug_args=""
if [ "$enable_debug" = "true" ]; then
    debug_args="--debug_loss"
fi



CUDA_VISIBLE_DEVICES=0 python vptta.py \
--dataset_root $dataset_root --model_root $model_root --path_save_log $path_save_log \
--Source_Dataset $Source \
--prompt_alpha $prompt_alpha --warm_n $warm_n \
--generator_lr $generator_lr \
--context_dim $context_dim \
--hypernetwork_hidden $hypernetwork_hidden \
--prompt_min_val $prompt_min_val \
--prompt_max_val $prompt_max_val \
--entropy_weight $entropy_weight \
--entropy_temperature $entropy_temperature \
--seed $seed \
$debug_args