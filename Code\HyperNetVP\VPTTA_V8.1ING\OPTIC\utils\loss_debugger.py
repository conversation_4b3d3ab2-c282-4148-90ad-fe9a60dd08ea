import os
import sys
import datetime


class LossDebugger:
    """
    损失函数调试器，用于监控训练过程中的损失变化和性能指标
    """
    
    def __init__(self, enabled=False, log_file=None):
        """
        初始化调试器
        
        Args:
            enabled (bool): 是否启用调试输出
            log_file (str): 调试日志文件路径，如果为None则输出到控制台
        """
        self.enabled = enabled
        self.log_file = log_file
        self.log_handle = None
        
        if self.enabled and self.log_file:
            # 创建调试日志文件
            log_dir = os.path.dirname(self.log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
            self.log_handle = open(self.log_file, 'w')
            self._write_header()
    
    def _write_header(self):
        """写入调试日志头部信息"""
        if self.enabled:
            header = f"=== Loss Debug Log Started at {datetime.datetime.now()} ==="
            self._write_line(header)
    
    def _write_line(self, message):
        """写入一行调试信息"""
        if not self.enabled:
            return
            
        if self.log_handle:
            self.log_handle.write(message + '\n')
            self.log_handle.flush()
        else:
            print(message)
    
    def log_sample_debug(self, sample_idx, iter_idx, losses_dict, metrics_dict=None):
        """
        记录单个样本的调试信息

        Args:
            sample_idx (int): 样本索引
            iter_idx (int): 迭代索引
            losses_dict (dict): 损失字典，包含各种损失值
            metrics_dict (dict): 性能指标字典，包含DICE分数等
        """
        if not self.enabled:
            return
        
        # 格式化样本和迭代信息
        sample_str = f"Sample_{sample_idx:04d}"
        iter_str = f"iter_{iter_idx}"
        
        # 格式化损失信息
        bn_loss = losses_dict.get('bn_loss', 0.0)
        entropy_loss = losses_dict.get('entropy_loss', 0.0)
        total_loss = losses_dict.get('total_loss', 0.0)

        loss_str = f"BN_Loss: {bn_loss:.4f} | Entropy_Loss: {entropy_loss:.4f} | Total_Loss: {total_loss:.4f}"
        
        # 格式化性能指标信息
        metrics_str = ""
        if metrics_dict:
            disc_dice = metrics_dict.get('disc_dice', 0.0)
            cup_dice = metrics_dict.get('cup_dice', 0.0)
            metrics_str = f" | Disc_Dice: {disc_dice:.2f} | Cup_Dice: {cup_dice:.2f}"

        # 组合完整的调试信息
        debug_message = f"[DEBUG] {sample_str} | {iter_str} | {loss_str}{metrics_str}"
        
        self._write_line(debug_message)
    
    def log_health_debug(self, sample_idx, mlp_data, grad_data, prompt_stats=None):
        """
        记录健康监控调试信息

        Args:
            sample_idx (int): 样本索引
            mlp_data (dict): 动态CNN数据
            grad_data (dict): 梯度数据
            prompt_stats (dict): 提示统计信息
        """
        if not self.enabled:
            return

        # 格式化样本信息
        sample_str = f"Sample_{sample_idx:04d}"

        # 格式化动态CNN信息
        mlp_norm = mlp_data.get('output_norm', 0.0)
        mlp_str = f"DynamicCNN: out_norm={mlp_norm:.2f}"

        # 格式化Grad信息
        grad_norm = grad_data.get('norm', 0.0)
        grad_status = grad_data.get('status', 'UNKNOWN')
        grad_str = f"Grad: norm={grad_norm:.4f},status={grad_status}"

        # 格式化提示统计信息
        prompt_str = ""
        if prompt_stats:
            prompt_mean = prompt_stats.get('mean', 0.0)
            prompt_std = prompt_stats.get('std', 0.0)
            prompt_min = prompt_stats.get('min', 0.0)
            prompt_max = prompt_stats.get('max', 0.0)
            prompt_str = f" | Prompt: mean={prompt_mean:.3f},std={prompt_std:.3f},range=[{prompt_min:.3f},{prompt_max:.3f}]"

        # 组合完整的健康监控信息
        health_message = f"[HEALTH] {sample_str} | {mlp_str} | {grad_str}{prompt_str}"

        self._write_line(health_message)

    def close(self):
        """关闭调试器，清理资源"""
        if self.log_handle:
            self._write_line(f"=== Loss Debug Log Ended at {datetime.datetime.now()} ===")
            self.log_handle.close()
            self.log_handle = None

    def __del__(self):
        """析构函数，确保文件句柄被正确关闭"""
        self.close()
