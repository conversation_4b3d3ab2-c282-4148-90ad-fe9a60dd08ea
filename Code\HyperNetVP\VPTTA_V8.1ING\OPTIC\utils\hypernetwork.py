import torch
import torch.nn as nn
import torch.nn.functional as F


class Hypernetwork(nn.Module):
    """
    超网络 - 根据情境向量生成目标CNN的所有权重和偏置
    
    架构：情境向量 → 3层MLP → 目标CNN参数
    """
    
    def __init__(self, context_dim=64, hidden_dim=256, target_param_count=23027):
        """
        初始化超网络
        
        Args:
            context_dim (int): 输入情境向量维度
            hidden_dim (int): 隐藏层维度
            target_param_count (int): 目标CNN总参数数量
        """
        super().__init__()
        self.context_dim = context_dim
        self.hidden_dim = hidden_dim
        self.target_param_count = target_param_count
        
        # 3层MLP架构
        self.mlp = nn.Sequential(
            # 第一层：context_dim → hidden_dim
            nn.Linear(context_dim, hidden_dim),
            nn.ReLU(inplace=True),
            
            # 第二层：hidden_dim → hidden_dim
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(inplace=True),
            
            # 第三层：hidden_dim → target_param_count
            nn.Linear(hidden_dim, target_param_count)
            # 注意：最后一层无激活函数，直接输出参数值
        )
        
        # 权重初始化
        self._initialize_weights()
    
    def _initialize_weights(self):
        """权重初始化策略"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # 使用Xavier初始化
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    # 最后一层偏置初始化为小值，其他层为0
                    if m.out_features == self.target_param_count:
                        nn.init.normal_(m.bias, mean=0.0, std=0.01)
                    else:
                        nn.init.zeros_(m.bias)
    
    def forward(self, context):
        """
        前向传播：生成目标CNN参数
        
        Args:
            context (torch.Tensor): 情境向量 (B, context_dim)
            
        Returns:
            torch.Tensor: 目标CNN所有参数 (B, target_param_count)
        """
        # 通过MLP生成参数
        params = self.mlp(context)  # (B, target_param_count)
        
        return params
    
    def get_parameter_count(self):
        """
        获取超网络参数统计
        
        Returns:
            dict: 参数统计信息
        """
        total_params = sum(p.numel() for p in self.parameters())
        
        # 分层统计
        layer_params = []
        for i, layer in enumerate(self.mlp):
            if isinstance(layer, nn.Linear):
                layer_param_count = sum(p.numel() for p in layer.parameters())
                layer_params.append({
                    f'layer_{i//2 + 1}': layer_param_count
                })
        
        return {
            'total_params': total_params,
            'layer_breakdown': layer_params,
            'output_param_count': self.target_param_count
        }


def calculate_target_cnn_params():
    """
    计算目标CNN的参数数量

    目标CNN架构：
    - Layer1: Conv2d(6, 32, 7, stride=8, padding=3) + bias
    - Layer2: Conv2d(32, 16, 5, stride=8, padding=2) + bias
    - Layer3: Conv2d(16, 3, 4, stride=1, padding=0) + bias

    Returns:
        dict: 参数统计信息
    """
    # Layer1: 6×32×7×7 + 32 = 9,408 + 32 = 9,440
    layer1_params = 6 * 32 * 7 * 7 + 32

    # Layer2: 32×16×5×5 + 16 = 12,800 + 16 = 12,816
    layer2_params = 32 * 16 * 5 * 5 + 16

    # Layer3: 16×3×4×4 + 3 = 768 + 3 = 771
    layer3_params = 16 * 3 * 4 * 4 + 3

    total_params = layer1_params + layer2_params + layer3_params

    return {
        'layer1_params': layer1_params,
        'layer2_params': layer2_params,
        'layer3_params': layer3_params,
        'total_params': total_params
    }


if __name__ == "__main__":
    # 测试参数计算
    param_info = calculate_target_cnn_params()
    print("目标CNN参数统计：")
    for key, value in param_info.items():
        print(f"  {key}: {value}")
    
    # 测试超网络
    hypernet = Hypernetwork(context_dim=64, hidden_dim=256, target_param_count=param_info['total_params'])
    print(f"\n超网络参数统计：")
    stats = hypernet.get_parameter_count()
    for key, value in stats.items():
        print(f"  {key}: {value}")
