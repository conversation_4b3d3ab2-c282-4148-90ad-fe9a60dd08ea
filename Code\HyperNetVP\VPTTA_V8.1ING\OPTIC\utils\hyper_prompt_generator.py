import torch
import torch.nn as nn
import torch.nn.functional as F
from .context_encoder import ContextEncoder
from .hypernetwork import Hypernetwork, calculate_target_cnn_params
from .dynamic_prompt_cnn import DynamicPromptCNN


class HyperPromptGenerator(nn.Module):
    """
    超网络提示生成器 - 整合情境编码器、超网络和动态CNN

    架构流程：
    1. 输入图像 → ContextEncoder → 情境向量
    2. 情境向量 → Hypernetwork → 动态CNN参数
    3. 输入图像 + 动态参数 → DynamicPromptCNN → 提示矩阵
    """

    def __init__(self, prompt_alpha=0.01, image_size=512,
                 context_dim=64, hypernetwork_hidden=256,
                 prompt_min_val=0.5, prompt_max_val=1.5, model_root=None):
        """
        初始化超网络提示生成器

        Args:
            prompt_alpha (float): 提示大小比例
            image_size (int): 参考图像尺寸
            context_dim (int): 情境向量维度
            hypernetwork_hidden (int): 超网络隐藏层维度
            prompt_min_val (float): 提示值最小值
            prompt_max_val (float): 提示值最大值
            model_root (str): 模型根目录路径
        """
        super().__init__()

        # 计算提示相关参数
        self.prompt_size = int(image_size * prompt_alpha) if int(image_size * prompt_alpha) > 1 else 1
        self.neutral_val = 1.0

        # 计算目标CNN参数数量
        param_info = calculate_target_cnn_params()
        self.target_param_count = param_info['total_params']

        # 初始化三个核心组件
        self.context_encoder = ContextEncoder(
            context_dim=context_dim,
            model_root=model_root
        )
        
        self.hypernetwork = Hypernetwork(
            context_dim=context_dim,
            hidden_dim=hypernetwork_hidden,
            target_param_count=self.target_param_count
        )
        
        self.dynamic_cnn = DynamicPromptCNN(
            prompt_size=self.prompt_size,
            prompt_min_val=prompt_min_val,
            prompt_max_val=prompt_max_val
        )
        
        # 验证参数数量一致性
        dynamic_param_count = self.dynamic_cnn.get_total_param_count()
        if dynamic_param_count != self.target_param_count:
            raise ValueError(f"参数数量不匹配: 超网络输出{self.target_param_count}, 动态CNN需要{dynamic_param_count}")
    
    def generate_prompt(self, x):
        """
        生成提示矩阵
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            
        Returns:
            tuple: (prompt, None) - 保持接口兼容
        """
        # 1. 提取情境特征
        context = self.context_encoder(x)  # (B, context_dim)
        
        # 2. 生成动态CNN参数
        dynamic_params = self.hypernetwork(context)  # (B, target_param_count)
        
        # 3. 使用动态参数生成提示
        prompt = self.dynamic_cnn(x, dynamic_params)  # (B, 3, prompt_size, prompt_size)
        
        return prompt, None
    
    def _apply_prompt_fft(self, x, prompt):
        """
        应用提示到图像的FFT域
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            prompt (torch.Tensor): 提示矩阵 (B, 3, prompt_size, prompt_size)
            
        Returns:
            tuple: (prompt_x, low_freq)
        """
        _, _, imgH, imgW = x.size()
        
        # FFT变换
        fft = torch.fft.fft2(x.clone(), dim=(-2, -1))
        amp_src, pha_src = torch.abs(fft), torch.angle(fft)
        amp_src = torch.fft.fftshift(amp_src)
        
        # 动态计算padding_size
        padding_size_w = (imgW - self.prompt_size) // 2
        padding_size_h = (imgH - self.prompt_size) // 2
        
        left_pad = padding_size_w
        right_pad = imgW - padding_size_w - self.prompt_size
        top_pad = padding_size_h
        bottom_pad = imgH - padding_size_h - self.prompt_size
        
        prompt_padded = F.pad(prompt, [left_pad, right_pad, top_pad, bottom_pad],
                              mode='constant', value=self.neutral_val).contiguous()
        
        # 尺寸验证
        if amp_src.shape != prompt_padded.shape:
            raise RuntimeError(f"Tensor size mismatch: {amp_src.shape} vs {prompt_padded.shape}")
        
        # 幅度调制
        amp_src_ = amp_src * prompt_padded
        amp_src_ = torch.fft.ifftshift(amp_src_)
        
        # 提取低频特征
        amp_low_ = amp_src[:, :, padding_size_h:padding_size_h+self.prompt_size,
                           padding_size_w:padding_size_w+self.prompt_size]
        
        # 逆FFT变换
        src_in_trg = self.iFFT(amp_src_, pha_src, imgH, imgW)
        
        return src_in_trg, amp_low_
    
    def iFFT(self, amp_src_, pha_src, imgH, imgW):
        """
        逆FFT变换方法
        
        Args:
            amp_src_: 幅度谱
            pha_src: 相位谱
            imgH: 图像高度
            imgW: 图像宽度
            
        Returns:
            torch.Tensor: 重构的图像
        """
        real = torch.cos(pha_src) * amp_src_
        imag = torch.sin(pha_src) * amp_src_
        fft_src_ = torch.complex(real=real, imag=imag)
        src_in_trg = torch.fft.ifft2(fft_src_, dim=(-2, -1), s=[imgH, imgW]).real
        return src_in_trg
    
    def parameters(self):
        """
        返回可训练参数（仅超网络参数）
        
        Returns:
            generator: 参数生成器
        """
        return self.hypernetwork.parameters()
    
    def train(self, mode=True):
        """
        设置训练模式
        
        Args:
            mode (bool): 训练模式标志
        """
        # 情境编码器始终保持eval模式（冻结）
        self.context_encoder.eval()
        # 超网络和动态CNN跟随模式设置
        self.hypernetwork.train(mode)
        self.dynamic_cnn.train(mode)
        return self
    
    def eval(self):
        """设置评估模式"""
        self.context_encoder.eval()
        self.hypernetwork.eval() 
        self.dynamic_cnn.eval()
        return self
    
    def get_parameter_stats(self):
        """
        获取详细的参数统计信息
        
        Returns:
            dict: 参数统计
        """
        context_stats = self.context_encoder.get_parameter_count()
        hyper_stats = self.hypernetwork.get_parameter_count()
        
        total_params = context_stats['total_params'] + hyper_stats['total_params']
        trainable_params = hyper_stats['total_params']  # 只有超网络可训练
        
        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'frozen_params': context_stats['total_params'],
            'context_encoder': context_stats,
            'hypernetwork': hyper_stats,
            'target_cnn_param_count': self.target_param_count
        }
