import torch
import torch.nn.functional as F


def orthogonal_loss(x_causal, x_spurious):
    """
    计算因果与虚假特征的正交损失
    
    Args:
        x_causal (torch.Tensor): 因果特征图 (B, C, H, W)
        x_spurious (torch.Tensor): 虚假特征图 (B, C, H, W)
        
    Returns:
        torch.Tensor: 正交损失标量值
    """
    # 将特征图展平为向量
    x_c_flat = x_causal.view(x_causal.size(0), -1)  # (B, C*H*W)
    x_s_flat = x_spurious.view(x_spurious.size(0), -1)  # (B, C*H*W)
    
    # 计算协方差矩阵的Frobenius范数
    # 协方差矩阵: x_c_flat.T @ x_s_flat
    covariance = torch.mm(x_c_flat.T, x_s_flat)  # (C*H*W, C*H*W)
    
    # 返回Frobenius范数
    return torch.norm(covariance, p='fro')


def domain_loss(x_spurious, z_global_original):
    """
    计算领域损失 - 虚假特征应该能够重构原始全局特征
    
    Args:
        x_spurious (torch.Tensor): 虚假特征图 (B, C, H, W)
        z_global_original (torch.Tensor): 原始全局特征 (B, C)
        
    Returns:
        torch.Tensor: 领域损失标量值
    """
    # 从虚假特征图提取全局特征
    z_s_global = F.adaptive_avg_pool2d(x_spurious, (1, 1)).view(x_spurious.size(0), -1)  # (B, C)
    
    # 计算重构损失（MSE）
    return F.mse_loss(z_s_global, z_global_original.detach())


def dice_loss(pred, target, smooth=1e-6):
    """
    计算Dice损失
    
    Args:
        pred (torch.Tensor): 预测概率图 (B, C, H, W)
        target (torch.Tensor): 目标标签 (B, C, H, W)
        smooth (float): 平滑项避免除零
        
    Returns:
        torch.Tensor: Dice损失标量值
    """
    # 确保预测值在[0,1]范围内
    pred = torch.sigmoid(pred)
    
    # 计算每个类别的Dice系数
    dice_scores = []
    for i in range(pred.size(1)):  # 遍历每个类别
        pred_i = pred[:, i].contiguous().view(-1)
        target_i = target[:, i].contiguous().view(-1)
        
        intersection = (pred_i * target_i).sum()
        union = pred_i.sum() + target_i.sum()
        
        dice = (2.0 * intersection + smooth) / (union + smooth)
        dice_scores.append(dice)
    
    # 返回平均Dice损失
    mean_dice = torch.stack(dice_scores).mean()
    return 1.0 - mean_dice


def segmentation_loss(pred, target):
    """
    计算分割损失 - Dice损失与交叉熵损失的组合
    
    Args:
        pred (torch.Tensor): 预测logits (B, C, H, W)
        target (torch.Tensor): 目标标签 (B, C, H, W)
        
    Returns:
        torch.Tensor: 组合分割损失标量值
    """
    # Dice损失
    dice_loss_val = dice_loss(pred, target)
    
    # 交叉熵损失
    ce_loss_val = F.binary_cross_entropy_with_logits(pred, target)
    
    # 组合损失（等权重）
    return dice_loss_val + ce_loss_val


def generate_pseudo_labels(seg_output, confidence_threshold=0.9):
    """
    基于高置信度预测生成伪标签
    
    Args:
        seg_output (torch.Tensor): 分割输出logits (B, C, H, W)
        confidence_threshold (float): 置信度阈值
        
    Returns:
        torch.Tensor: 伪标签 (B, C, H, W)
    """
    # 转换为概率
    probs = torch.sigmoid(seg_output)
    
    # 生成伪标签：高置信度的预测
    pseudo_labels = torch.zeros_like(probs)
    
    # 对于每个类别，选择高置信度的像素
    for i in range(probs.size(1)):
        high_conf_mask = probs[:, i] > confidence_threshold
        pseudo_labels[:, i][high_conf_mask] = 1.0
        
        low_conf_mask = probs[:, i] < (1.0 - confidence_threshold)
        pseudo_labels[:, i][low_conf_mask] = 0.0
    
    return pseudo_labels
